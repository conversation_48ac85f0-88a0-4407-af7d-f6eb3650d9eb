from ragflow_sdk import RAG<PERSON><PERSON>, Agent

rag_object = RAGFlow(api_key="ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm", base_url="http://117.72.181.138:180")
AGENT_id = "92a162f6710011f097860242ac130006"
agent = rag_object.list_agents(id=AGENT_id)[0]
session = agent.create_session()

print("\n===== Miss R ====\n")
print("Hello. What can I do for you?")

while True:
    question = input("\n===== User ====\n> ")
    print("\n==== Miss R ====\n")

    cont = ""
    for ans in session.ask(question, stream=True):
        print(ans.content[len(cont):], end='', flush=True)
        cont = ans.content