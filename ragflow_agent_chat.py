#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow Agent 对话程序
基于RAGFlow Python SDK与Agent进行对话
"""

import sys
import time
from ragflow_sdk import RAGFlow

# 配置信息
API_KEY = "ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm"
BASE_URL = "http://117.72.181.138:180/"
AGENT_ID = "92a162f6710011f097860242ac130006"

class RAGFlowAgentChat:
    """RAGFlow Agent 对话类"""
    
    def __init__(self, api_key: str, base_url: str, agent_id: str):
        """
        初始化RAGFlow Agent对话客户端
        
        Args:
            api_key: RAGFlow API密钥
            base_url: RAGFlow服务器地址
            agent_id: Agent ID
        """
        self.api_key = api_key
        self.base_url = base_url
        self.agent_id = agent_id
        self.rag_client = None
        self.agent = None
        self.session = None
        
    def connect(self):
        """连接到RAGFlow服务"""
        try:
            print("正在连接到RAGFlow服务...")
            self.rag_client = RAGFlow(api_key=self.api_key, base_url=self.base_url)
            print("✓ 成功连接到RAGFlow服务")
            return True
        except Exception as e:
            print(f"✗ 连接RAGFlow服务失败: {e}")
            return False
    
    def get_agent(self):
        """获取指定的Agent"""
        try:
            print(f"正在获取Agent (ID: {self.agent_id})...")

            # 尝试不同的方法获取Agent
            try:
                # 方法1: 尝试list_agents
                if hasattr(self.rag_client, 'list_agents'):
                    agents = self.rag_client.list_agents(id=self.agent_id)
                    if agents:
                        self.agent = agents[0]
                        print(f"✓ 成功获取Agent: {getattr(self.agent, 'title', 'Unknown')}")
                        return True

                # 方法2: 尝试直接使用agent_id创建Agent对象
                print("尝试直接使用Agent ID...")
                from ragflow_sdk import Agent
                self.agent = Agent(self.rag_client, self.agent_id)
                print(f"✓ 成功创建Agent对象")
                return True

            except Exception as inner_e:
                print(f"尝试其他方法: {inner_e}")

                # 方法3: 检查可用的方法
                print("可用的方法:")
                methods = [method for method in dir(self.rag_client) if not method.startswith('_')]
                agent_methods = [method for method in methods if 'agent' in method.lower()]
                print(f"Agent相关方法: {agent_methods}")

                return False

        except Exception as e:
            print(f"✗ 获取Agent失败: {e}")
            print("请检查Agent ID是否正确，或者RAGFlow SDK版本是否兼容")
            return False
    
    def create_session(self):
        """创建对话会话"""
        try:
            print("正在创建对话会话...")
            self.session = self.agent.create_session()
            print(f"✓ 成功创建会话 (ID: {self.session.id})")
            
            # 显示欢迎消息
            if hasattr(self.session, 'message') and self.session.message:
                for msg in self.session.message:
                    if msg.get('role') == 'assistant':
                        print(f"\n🤖 Agent: {msg.get('content', '')}")
            
            return True
        except Exception as e:
            print(f"✗ 创建会话失败: {e}")
            return False
    
    def ask_question(self, question: str, stream: bool = True):
        """
        向Agent提问
        
        Args:
            question: 问题内容
            stream: 是否使用流式输出
        """
        try:
            if stream:
                print("\n🤖 Agent: ", end="", flush=True)
                content = ""
                for response in self.session.ask(question=question, stream=True):
                    new_content = response.content[len(content):]
                    print(new_content, end="", flush=True)
                    content = response.content
                print()  # 换行
                
                # 显示引用信息（如果有）
                if hasattr(response, 'reference') and response.reference:
                    self.show_references(response.reference)
                    
            else:
                response = self.session.ask(question=question, stream=False)
                print(f"\n🤖 Agent: {response.content}")
                
                # 显示引用信息（如果有）
                if hasattr(response, 'reference') and response.reference:
                    self.show_references(response.reference)
                    
        except Exception as e:
            print(f"\n✗ 提问失败: {e}")
    
    def show_references(self, references):
        """显示引用信息"""
        if not references:
            return
            
        print("\n📚 参考资料:")
        for i, ref in enumerate(references, 1):
            print(f"  [{i}] 文档: {getattr(ref, 'document_name', 'Unknown')}")
            if hasattr(ref, 'similarity'):
                print(f"      相似度: {ref.similarity:.3f}")
            if hasattr(ref, 'content') and len(ref.content) > 100:
                print(f"      内容摘要: {ref.content[:100]}...")
            elif hasattr(ref, 'content'):
                print(f"      内容: {ref.content}")
    
    def start_chat(self):
        """开始对话"""
        print("\n" + "="*60)
        print("🚀 RAGFlow Agent 对话程序")
        print("="*60)
        print("输入 'quit', 'exit', 'q' 退出程序")
        print("输入 'help' 查看帮助信息")
        print("输入 'clear' 清屏")
        print("="*60)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 您: ").strip()
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("\n👋 再见！")
                    break
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                elif user_input.lower() == 'clear':
                    import os
                    os.system('cls' if os.name == 'nt' else 'clear')
                    continue
                elif not user_input:
                    print("请输入您的问题...")
                    continue
                
                # 向Agent提问
                self.ask_question(user_input, stream=True)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"\n✗ 发生错误: {e}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
📖 帮助信息:
  - 直接输入问题与Agent对话
  - 'quit', 'exit', 'q': 退出程序
  - 'help': 显示此帮助信息
  - 'clear': 清屏
  - Ctrl+C: 强制退出
        """
        print(help_text)
    
    def run(self):
        """运行对话程序"""
        # 连接服务
        if not self.connect():
            return False
        
        # 获取Agent
        if not self.get_agent():
            return False
        
        # 创建会话
        if not self.create_session():
            return False
        
        # 开始对话
        self.start_chat()
        return True

def main():
    """主函数"""
    # 创建对话客户端
    chat_client = RAGFlowAgentChat(
        api_key=API_KEY,
        base_url=BASE_URL,
        agent_id=AGENT_ID
    )
    
    # 运行对话程序
    success = chat_client.run()
    
    if not success:
        print("\n程序启动失败，请检查配置信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
